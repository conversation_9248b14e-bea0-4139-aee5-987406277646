-- Roblox WindUI Script with 5 Tabs
-- Load WindUI Library
local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local RootPart = Character:WaitForChild("HumanoidRootPart")

-- Player Variables
local originalWalkSpeed = Humanoid.WalkSpeed
local originalJumpPower = Humanoid.JumpPower
local noclipEnabled = false
local infJumpEnabled = false
local noclipConnection

-- Create Window
local Window = WindUI:CreateWindow({
    Title = "Roblox Exploit Hub",
    Icon = "gamepad-2",
    Author = "WindUI Script",
    Folder = "RobloxHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    Resizable = true,
    SideBarWidth = 200,
})

-- Create Tabs (Tabs are created directly from Window, not Section)
local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
})

local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "map-pin",
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
})

local SettingsTab = Window:Tab({
    Title = "Settings",
    Icon = "settings",
})

-- MAIN TAB (Placeholders - not implemented yet)
MainTab:Toggle({
    Title = "Auto Lock",
    Desc = "Automatically lock onto targets",
    Icon = "target",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Auto Lock:", state)
        -- TODO: Implement Auto Lock functionality
    end
})

MainTab:Toggle({
    Title = "Instant Proximity",
    Desc = "Instant proximity detection",
    Icon = "zap",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Instant Proximity:", state)
        -- TODO: Implement Instant Proximity functionality
    end
})

MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Remove darkness from the game",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Full Bright:", state)
        -- TODO: Implement Full Bright functionality
    end
})

-- PLAYER TAB (Fully Implemented)
-- Speed Modifier
PlayerTab:Slider({
    Title = "Speed Modifier",
    Desc = "Adjust your walking speed",
    Step = 1,
    Value = {
        Min = 1,
        Max = 100,
        Default = 16,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.WalkSpeed = value
        end
    end
})

-- Jump Modifier
PlayerTab:Slider({
    Title = "Jump Modifier",
    Desc = "Adjust your jump power",
    Step = 1,
    Value = {
        Min = 1,
        Max = 200,
        Default = 50,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.JumpPower = value
        end
    end
})

-- Noclip Toggle
PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls and objects",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        noclipEnabled = state
        
        if noclipEnabled then
            noclipConnection = RunService.Stepped:Connect(function()
                if Character then
                    for _, part in pairs(Character:GetChildren()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end
            end)
        else
            if noclipConnection then
                noclipConnection:Disconnect()
                noclipConnection = nil
            end
            
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
                        part.CanCollide = true
                    end
                end
            end
        end
    end
})

-- Infinite Jump Toggle
PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump infinitely in the air",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        infJumpEnabled = state
    end
})

-- Infinite Jump Implementation
UserInputService.JumpRequest:Connect(function()
    if infJumpEnabled and Character and Character:FindFirstChild("Humanoid") then
        Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
    end
end)

-- Character Respawn Handler
LocalPlayer.CharacterAdded:Connect(function(newCharacter)
    Character = newCharacter
    Humanoid = Character:WaitForChild("Humanoid")
    RootPart = Character:WaitForChild("HumanoidRootPart")
    
    -- Reset values
    originalWalkSpeed = Humanoid.WalkSpeed
    originalJumpPower = Humanoid.JumpPower
    
    -- Reapply noclip if enabled
    if noclipEnabled and noclipConnection then
        noclipConnection:Disconnect()
        noclipConnection = RunService.Stepped:Connect(function()
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.CanCollide then
                        part.CanCollide = false
                    end
                end
            end
        end)
    end
end)

-- TELEPORT TAB (Placeholder)
TeleportTab:Toggle({
    Title = "Teleport System",
    Desc = "Teleportation features coming soon",
    Icon = "map-pin",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Teleport System:", state)
        -- TODO: Implement Teleport functionality
    end
})

-- ESP TAB (Placeholder)
ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "ESP features coming soon",
    Icon = "eye",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Player ESP:", state)
        -- TODO: Implement ESP functionality
    end
})

-- SETTINGS TAB (Placeholder)
SettingsTab:Toggle({
    Title = "Save Configuration",
    Desc = "Settings features coming soon",
    Icon = "save",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Save Configuration:", state)
        -- TODO: Implement Settings functionality
    end
})

print("WindUI Script Loaded Successfully!")
