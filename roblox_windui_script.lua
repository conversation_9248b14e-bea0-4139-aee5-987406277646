-- Roblox WindUI Script with 5 Tabs
-- Load WindUI Library
local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local RootPart = Character:WaitForChild("HumanoidRootPart")

-- Player Variables
local originalWalkSpeed = Humanoid.WalkSpeed
local originalJumpPower = Humanoid.JumpPower
local noclipEnabled = false
local infJumpEnabled = false
local noclipConnection
local autoLockEnabled = false
local autoLockConnection
local instantProximityEnabled = false
local proximityHoldDuration = 0.1
local modifiedPrompts = {}
local proximityConnection

-- Create Window
local Window = WindUI:CreateWindow({
    Title = "Roblox Exploit Hub",
    Icon = "gamepad-2",
    Author = "WindUI Script",
    Folder = "RobloxHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    Resizable = true,
    SideBarWidth = 200,
})

-- Create Tabs (Tabs are created directly from Window, not Section)
local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
})

local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "map-pin",
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
})

local SettingsTab = Window:Tab({
    Title = "Settings",
    Icon = "settings",
})

-- MAIN TAB (Placeholders - not implemented yet)
MainTab:Toggle({
    Title = "Auto Lock",
    Desc = "Auto-teleports the player when the countdown reaches 0s",
    Icon = "target",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        autoLockEnabled = state
        
        if autoLockEnabled then
            -- Start monitoring countdown
            autoLockConnection = RunService.Heartbeat:Connect(function()
                if LocalPlayer and LocalPlayer.Name then
                    local playerPath = workspace.Bases.Players:FindFirstChild(LocalPlayer.Name)
                    if playerPath then
                        local lockTouch = playerPath:FindFirstChild("LockTouch")
                        if lockTouch then
                            local status = lockTouch:FindFirstChild("Status")
                            if status then
                                local countdown = status:FindFirstChild("Countdown")
                                if countdown and countdown:IsA("TextLabel") then
                                    if countdown.Text == "0s" then
                                        -- Teleport player to LockTouch
                                        if Character and Character:FindFirstChild("HumanoidRootPart") then
                                            Character.HumanoidRootPart.CFrame = lockTouch.CFrame
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end)
        else
            -- Stop monitoring countdown
            if autoLockConnection then
                autoLockConnection:Disconnect()
                autoLockConnection = nil
            end
        end
    end
})

-- Proximity Hold Duration Slider
MainTab:Slider({
    Title = "Proximity Hold Duration",
    Desc = "Adjust ProximityPrompt hold duration",
    Step = 0.1,
    Value = {
        Min = 0.1,
        Max = 2.0,
        Default = 0.1,
    },
    Callback = function(value)
        proximityHoldDuration = value
        -- Update all currently modified prompts
        for prompt, originalDuration in pairs(modifiedPrompts) do
            if prompt and prompt.Parent then
                prompt.HoldDuration = proximityHoldDuration
            end
        end
    end
})

MainTab:Toggle({
    Title = "Instant Proximity",
    Desc = "Modifies all ProximityPrompts to use custom hold duration",
    Icon = "zap",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        instantProximityEnabled = state
        
        if instantProximityEnabled then
            -- Function to modify a proximity prompt
            local function modifyPrompt(prompt)
                if prompt:IsA("ProximityPrompt") and not modifiedPrompts[prompt] then
                    modifiedPrompts[prompt] = prompt.HoldDuration
                    prompt.HoldDuration = proximityHoldDuration
                end
            end
            
            -- Function to scan for proximity prompts efficiently
            local function scanForPrompts()
                -- Scan existing prompts
                for _, descendant in pairs(workspace:GetDescendants()) do
                    if descendant:IsA("ProximityPrompt") then
                        modifyPrompt(descendant)
                    end
                end
                
                -- Scan players for prompts
                for _, player in pairs(Players:GetPlayers()) do
                    if player.Character then
                        for _, descendant in pairs(player.Character:GetDescendants()) do
                            if descendant:IsA("ProximityPrompt") then
                                modifyPrompt(descendant)
                            end
                        end
                    end
                end
            end
            
            -- Initial scan
            scanForPrompts()
            
            -- Set up efficient monitoring for new prompts
            proximityConnection = workspace.DescendantAdded:Connect(function(descendant)
                if descendant:IsA("ProximityPrompt") then
                    modifyPrompt(descendant)
                end
            end)
            
            -- Monitor player characters for new prompts
            for _, player in pairs(Players:GetPlayers()) do
                if player.Character then
                    player.Character.DescendantAdded:Connect(function(descendant)
                        if descendant:IsA("ProximityPrompt") and instantProximityEnabled then
                            modifyPrompt(descendant)
                        end
                    end)
                end
            end
            
            -- Monitor new players
            Players.PlayerAdded:Connect(function(player)
                if instantProximityEnabled then
                    player.CharacterAdded:Connect(function(character)
                        if instantProximityEnabled then
                            character.DescendantAdded:Connect(function(descendant)
                                if descendant:IsA("ProximityPrompt") and instantProximityEnabled then
                                    modifyPrompt(descendant)
                                end
                            end)
                        end
                    end)
                end
            end)
        else
            -- Restore original hold durations
            for prompt, originalDuration in pairs(modifiedPrompts) do
                if prompt and prompt.Parent then
                    prompt.HoldDuration = originalDuration
                end
            end
            modifiedPrompts = {}
            
            -- Disconnect monitoring
            if proximityConnection then
                proximityConnection:Disconnect()
                proximityConnection = nil
            end
        end
    end
})

MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Remove darkness from the game",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Full Bright:", state)
        -- TODO: Implement Full Bright functionality
    end
})

-- PLAYER TAB (Fully Implemented)
-- Speed Modifier
PlayerTab:Slider({
    Title = "Speed Modifier",
    Desc = "Adjust your walking speed",
    Step = 1,
    Value = {
        Min = 1,
        Max = 100,
        Default = 16,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.WalkSpeed = value
        end
    end
})

-- Jump Modifier
PlayerTab:Slider({
    Title = "Jump Modifier",
    Desc = "Adjust your jump power",
    Step = 1,
    Value = {
        Min = 1,
        Max = 200,
        Default = 50,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.JumpPower = value
        end
    end
})

-- Noclip Toggle
PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls and objects",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        noclipEnabled = state
        
        if noclipEnabled then
            noclipConnection = RunService.Stepped:Connect(function()
                if Character then
                    for _, part in pairs(Character:GetChildren()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end
            end)
        else
            if noclipConnection then
                noclipConnection:Disconnect()
                noclipConnection = nil
            end
            
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
                        part.CanCollide = true
                    end
                end
            end
        end
    end
})

-- Infinite Jump Toggle
PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump infinitely in the air",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        infJumpEnabled = state
    end
})

-- Infinite Jump Implementation
UserInputService.JumpRequest:Connect(function()
    if infJumpEnabled and Character and Character:FindFirstChild("Humanoid") then
        Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
    end
})

-- Character Respawn Handler
LocalPlayer.CharacterAdded:Connect(function(newCharacter)
    Character = newCharacter
    Humanoid = Character:WaitForChild("Humanoid")
    RootPart = Character:WaitForChild("HumanoidRootPart")
    
    -- Reset values
    originalWalkSpeed = Humanoid.WalkSpeed
    originalJumpPower = Humanoid.JumpPower
    
    -- Reapply noclip if enabled
    if noclipEnabled and noclipConnection then
        noclipConnection:Disconnect()
        noclipConnection = RunService.Stepped:Connect(function()
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.CanCollide then
                        part.CanCollide = false
                    end
                end
            end
        end)
    end
    
    -- Reapply auto lock if enabled
    if autoLockEnabled and autoLockConnection then
        autoLockConnection:Disconnect()
        autoLockConnection = RunService.Heartbeat:Connect(function()
            if LocalPlayer and LocalPlayer.Name then
                local playerPath = workspace.Bases.Players:FindFirstChild(LocalPlayer.Name)
                if playerPath then
                    local lockTouch = playerPath:FindFirstChild("LockTouch")
                    if lockTouch then
                        local status = lockTouch:FindFirstChild("Status")
                        if status then
                            local countdown = status:FindFirstChild("Countdown")
                            if countdown and countdown:IsA("TextLabel") then
                                if countdown.Text == "0s" then
                                    -- Teleport player to LockTouch
                                    if Character and Character:FindFirstChild("HumanoidRootPart") then
                                        Character.HumanoidRootPart.CFrame = lockTouch.CFrame
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end)
    end
    
    -- Reapply proximity prompt monitoring if enabled
    if instantProximityEnabled then
        -- Monitor new character for proximity prompts
        Character.DescendantAdded:Connect(function(descendant)
            if descendant:IsA("ProximityPrompt") and instantProximityEnabled then
                if not modifiedPrompts[descendant] then
                    modifiedPrompts[descendant] = descendant.HoldDuration
                    descendant.HoldDuration = proximityHoldDuration
                end
            end
        end)
        
        -- Check existing prompts in new character
        for _, descendant in pairs(Character:GetDescendants()) do
            if descendant:IsA("ProximityPrompt") and not modifiedPrompts[descendant] then
                modifiedPrompts[descendant] = descendant.HoldDuration
                descendant.HoldDuration = proximityHoldDuration
            end
        end
    end
end)

-- TELEPORT TAB (Placeholder)
TeleportTab:Toggle({
    Title = "Teleport System",
    Desc = "Teleportation features coming soon",
    Icon = "map-pin",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Teleport System:", state)
        -- TODO: Implement Teleport functionality
    end
})

-- ESP TAB (Placeholder)
ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "ESP features coming soon",
    Icon = "eye",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Player ESP:", state)
        -- TODO: Implement ESP functionality
    end
})

-- SETTINGS TAB (Placeholder)
SettingsTab:Toggle({
    Title = "Save Configuration",
    Desc = "Settings features coming soon",
    Icon = "save",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Save Configuration:", state)
        -- TODO: Implement Settings functionality
    end
})

print("WindUI Script Loaded Successfully!")
